from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import Dict, Optional, Any
import json
import uuid


@dataclass
class WorkflowState:
    """
    WorkflowState 数据模型
    用于跟踪自动化工作流的当前状态与进度。
    """

    workflow_id: str
    current_stage: str
    project_path: str
    documents_status: Dict[str, Any] = field(default_factory=dict)
    epic_progress: Dict[str, Any] = field(default_factory=dict)
    story_progress: Dict[str, Any] = field(default_factory=dict)
    created_at: str = field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")
    updated_at: str = field(default_factory=lambda: datetime.utcnow().isoformat() + "Z")

    def __post_init__(self) -> None:
        # 基本校验
        if not isinstance(self.workflow_id, str) or not self.workflow_id:
            raise ValueError("workflow_id must be a non-empty string")
        if not isinstance(self.current_stage, str) or not self.current_stage:
            raise ValueError("current_stage must be a non-empty string")
        if not isinstance(self.project_path, str) or not self.project_path:
            raise ValueError("project_path must be a non-empty string")
        # ensure timestamps are strings
        if not isinstance(self.created_at, str):
            self.created_at = datetime.utcnow().isoformat() + "Z"
        if not isinstance(self.updated_at, str):
            self.updated_at = datetime.utcnow().isoformat() + "Z"

    @staticmethod
    def new(project_path: str, initial_stage: str = "document_processing") -> "WorkflowState":
        """
        创建一个新的 WorkflowState 实例，自动生成 workflow_id 和时间戳。
        """
        return WorkflowState(
            workflow_id=str(uuid.uuid4()),
            current_stage=initial_stage,
            project_path=project_path,
        )

    def touch(self) -> None:
        """更新 updated_at 时间戳"""
        self.updated_at = datetime.utcnow().isoformat() + "Z"

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典，便于存储为 JSON/string 等"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "WorkflowState":
        """从字典反序列化为 WorkflowState，兼容 JSON 存储"""
        # Defensive copy and basic normalization
        d = dict(data)
        # Ensure required fields exist
        required = ["workflow_id", "current_stage", "project_path"]
        for k in required:
            if k not in d:
                raise ValueError(f"Missing required field '{k}' for WorkflowState")
        # Parse JSON strings if some fields were stored as JSON strings
        for key in ("documents_status", "epic_progress", "story_progress"):
            if key in d and isinstance(d[key], str):
                try:
                    d[key] = json.loads(d[key])
                except Exception:
                    d[key] = {}
        return cls(
            workflow_id=d["workflow_id"],
            current_stage=d["current_stage"],
            project_path=d["project_path"],
            documents_status=d.get("documents_status", {}),
            epic_progress=d.get("epic_progress", {}),
            story_progress=d.get("story_progress", {}),
            created_at=d.get("created_at", datetime.utcnow().isoformat() + "Z"),
            updated_at=d.get("updated_at", datetime.utcnow().isoformat() + "Z"),
        )