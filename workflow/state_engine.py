from typing import Dict, Any, List
from pathlib import Path
import json
import logging

logger = logging.getLogger(__name__)


def scan_project_structure(project_path: str) -> Dict[str, Any]:
    """
    扫描项目目录，识别 docs/ 中的核心文档、prd/ 和 architecture/ 分片文件，以及 epics/stories 文件。
    返回结构化的扫描结果字典。
    """
    p = Path(project_path)
    result: Dict[str, Any] = {
        "project_path": str(p),
        "exists": p.exists(),
        "docs": {"exists": False, "files": []},
        "core_docs": {"brief.md": False, "prd.md": False, "architecture.md": False},
        "prd_shards": [],
        "architecture_shards": [],
        "epics": [],
        "stories": [],
    }

    try:
        docs_dir = p / "docs"
        result["docs"]["exists"] = docs_dir.exists() and docs_dir.is_dir()
        if not result["docs"]["exists"]:
            return result

        # List files in docs root
        for f in docs_dir.iterdir():
            if f.is_file():
                name = f.name
                result["docs"]["files"].append(name)
                if name in result["core_docs"]:
                    result["core_docs"][name] = True

        # scan prd/ and architecture/ subfolders
        prd_dir = docs_dir / "prd"
        if prd_dir.exists() and prd_dir.is_dir():
            for f in prd_dir.rglob("*.md"):
                result["prd_shards"].append(str(f.relative_to(p)))

        arch_dir = docs_dir / "architecture"
        if arch_dir.exists() and arch_dir.is_dir():
            for f in arch_dir.rglob("*.md"):
                result["architecture_shards"].append(str(f.relative_to(p)))

