# BMAD Agent FastMCP Service v2.0 Product Requirements Document (PRD)

## Table of Contents

- [BMAD Agent FastMCP Service v2.0 Product Requirements Document (PRD)](#table-of-contents)
  - [Goals and Background Context](./goals-and-background-context.md)
    - [Goals](./goals-and-background-context.md#goals)
    - [Background Context](./goals-and-background-context.md#background-context)
    - [Change Log](./goals-and-background-context.md#change-log)
  - [Requirements](./requirements.md)
    - [Functional](./requirements.md#functional)
    - [Non Functional](./requirements.md#non-functional)
  - [Technical Assumptions](./technical-assumptions.md)
    - [Repository Structure: Monorepo](./technical-assumptions.md#repository-structure-monorepo)
    - [Service Architecture](./technical-assumptions.md#service-architecture)
    - [Testing Requirements](./technical-assumptions.md#testing-requirements)
    - [Additional Technical Assumptions and Requests](./technical-assumptions.md#additional-technical-assumptions-and-requests)
  - [Epic List](./epic-list.md)
  - [Epic 1: 核心自动化引擎](./epic-1-核心自动化引擎.md)
    - [Story 1.1: 工作流程状态检测系统](./epic-1-核心自动化引擎.md#story-11-工作流程状态检测系统)
      - [Acceptance Criteria](./epic-1-核心自动化引擎.md#acceptance-criteria)
    - [Story 1.2: 文档自动分片功能](./epic-1-核心自动化引擎.md#story-12-文档自动分片功能)
      - [Acceptance Criteria](./epic-1-核心自动化引擎.md#acceptance-criteria)
    - [Story 1.3: Epic 自动创建系统](./epic-1-核心自动化引擎.md#story-13-epic-自动创建系统)
      - [Acceptance Criteria](./epic-1-核心自动化引擎.md#acceptance-criteria)
  - [Epic 2: 智能体协作系统](./epic-2-智能体协作系统.md)
    - [Story 2.1: 智能体任务传递机制](./epic-2-智能体协作系统.md#story-21-智能体任务传递机制)
      - [Acceptance Criteria](./epic-2-智能体协作系统.md#acceptance-criteria)
    - [Story 2.2: 上下文自动加载系统](./epic-2-智能体协作系统.md#story-22-上下文自动加载系统)
      - [Acceptance Criteria](./epic-2-智能体协作系统.md#acceptance-criteria)
  - [Epic 3: 真实任务执行](./epic-3-真实任务执行.md)
    - [Story 3.1: 代码生成引擎](./epic-3-真实任务执行.md#story-31-代码生成引擎)
      - [Acceptance Criteria](./epic-3-真实任务执行.md#acceptance-criteria)
    - [Story 3.2: 测试自动生成](./epic-3-真实任务执行.md#story-32-测试自动生成)
      - [Acceptance Criteria](./epic-3-真实任务执行.md#acceptance-criteria)
  - [Epic 4: 完整工作流程集成](./epic-4-完整工作流程集成.md)
    - [Story 4.1: 一键启动工作流程](./epic-4-完整工作流程集成.md#story-41-一键启动工作流程)
      - [Acceptance Criteria](./epic-4-完整工作流程集成.md#acceptance-criteria)
    - [Story 4.2: 错误恢复和重试机制](./epic-4-完整工作流程集成.md#story-42-错误恢复和重试机制)
      - [Acceptance Criteria](./epic-4-完整工作流程集成.md#acceptance-criteria)
  - [Next Steps](./next-steps.md)
    - [UX Expert Prompt](./next-steps.md#ux-expert-prompt)
    - [Architect Prompt](./next-steps.md#architect-prompt)
