# Story 1.1: 工作流程状态检测系统

## Status
Appove

## Story
**As a** developer,
**I want** the system to automatically detect the current workflow stage,
**so that** I don't need to manually track project progress.

## Acceptance Criteria
1. 系统能够扫描项目文件结构，识别当前处于哪个开发阶段
2. 系统能够检测 docs/ 文件夹中的核心文档（brief.md, prd.md, architecture.md）
3. 系统能够识别已存在的 Epic 和 Story 文件
4. 系统能够确定下一个需要执行的工作流程步骤
5. 提供清晰的状态报告，显示当前进度和下一步行动

## Tasks / Subtasks
- [ ] Task 1: 创建 WorkflowState 数据模型 (AC: 1, 4)
  - [ ] 在 workflow/models.py 中实现 WorkflowState 类
  - [ ] 包含所有必需属性：workflow_id, current_stage, project_path, documents_status, epic_progress, story_progress
  - [ ] 添加类型提示和数据验证
- [ ] Task 2: 实现文件结构扫描功能 (AC: 1, 2, 3)
  - [ ] 在 workflow/state_engine.py 中创建 scan_project_structure 方法
  - [ ] 检测 docs/ 文件夹中的核心文档存在性
  - [ ] 扫描 docs/prd/ 和 docs/architecture/ 分片文件
  - [ ] 识别现有的 Epic 和 Story 文件
- [ ] Task 3: 实现工作流程阶段检测逻辑 (AC: 4)
  - [ ] 创建阶段检测算法，基于文件存在性确定当前阶段
  - [ ] 实现下一步行动建议逻辑
  - [ ] 处理边界情况和错误状态
- [ ] Task 4: 创建状态报告生成器 (AC: 5)
  - [ ] 实现 generate_status_report 方法
  - [ ] 格式化输出当前进度和下一步行动
  - [ ] 包含详细的文件状态信息
- [ ] Task 5: 集成到 FastMCP 服务 (AC: 1-5)
  - [ ] 在 bmad_agent_mcp.py 中添加新的 MCP 工具
  - [ ] 实现 get_workflow_status 工具接口
  - [ ] 确保与现有工具的兼容性

## Dev Notes

### Previous Story Insights
这是第一个故事，没有前置故事的经验。

### Data Models
**WorkflowState** [Source: architecture/data-models.md#workflowstate]
- workflow_id: str - 工作流程实例的唯一标识符
- current_stage: str - 当前工作流程阶段（document_processing, epic_creation, story_development 等）
- project_path: str - 项目根目录路径
- documents_status: dict - 文档处理状态（已分片、已验证等）
- epic_progress: dict - 每个史诗的进度跟踪
- story_progress: dict - 每个故事的进度跟踪
- created_at: datetime - 工作流程创建时间戳
- updated_at: datetime - 最后更新时间戳

### Component Specifications
**Workflow State Engine** [Source: architecture/components.md#workflow-state-engine]
- 核心接口：
  - `start_workflow(project_path: str) -> WorkflowState`
  - `get_workflow_status(workflow_id: str) -> WorkflowState`
- 依赖：SQLite 数据库、Document Processor、Agent Orchestrator
- 技术栈：Python asyncio、SQLite、dataclasses 用于状态管理

### File Locations
基于项目结构 [Source: architecture/source-tree.md]：
- 主要实现：`workflow/state_engine.py`
- 数据模型：`workflow/models.py`
- MCP 集成：`bmad_agent_mcp.py`
- 数据库模式：`database/schema.sql`

### Technical Constraints
- Python 3.8+ 与严格的类型提示 [Source: architecture/coding-standards.md#core-standards]
- 使用 Python logging 模块，禁止 print() 语句 [Source: architecture/coding-standards.md#critical-rules]
- 所有文件操作必须包含适当的异常处理 [Source: architecture/coding-standards.md#critical-rules]
- 工作流程状态变更必须是原子性的并记录日志 [Source: architecture/coding-standards.md#critical-rules]

### Testing
**测试要求** [Source: architecture/test-strategy-and-standards.md]
- 框架：pytest 7.4+
- 位置：tests/unit/ 镜像源代码结构
- 覆盖率：新工作流程组件需要 90% 覆盖率
- 模拟：使用 unittest.mock 模拟所有外部依赖（文件系统、数据库）
- 模式：遵循 AAA 模式（Arrange, Act, Assert）
- 为所有公共方法生成测试
- 覆盖边界情况和错误条件

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-12 | 1.0 | Initial story creation | SM Agent |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled by dev agent*

### Debug Log References
*To be filled by dev agent*

### Completion Notes List
*To be filled by dev agent*

### File List
*To be filled by dev agent*

## QA Results
*Results from QA Agent review will be populated here*
